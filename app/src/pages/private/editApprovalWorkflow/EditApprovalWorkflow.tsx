import ApprovalWorkflowForm from '@/components/approvalWorkflow/ApprovalWorkflowForm';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Typography from '@/components/ui/Typography';
import { useOrganizationContext } from '@/context/OrganizationContext';
import { useApprovalWorkflow, useUpdateApprovalWorkflow } from '@/hooks/useApprovalWorkflow';
import { approvalWorkflowsRoute } from '@/routes/private/approvalWorkflows.route';
import { UpdateApprovalWorkflowRequest } from '@/types/approvalWorkflow.types';
import { useNavigate, useParams } from '@tanstack/react-router';
import React, { useEffect, useRef } from 'react';
import './EditApprovalWorkflow.css';

const EditApprovalWorkflow: React.FC = () => {
  const navigate = useNavigate();
  const params = useParams({ strict: false });
  const id = (params as any).id;
  const toast = useRef<ToastRef>(null);
  const { organizationId } = useOrganizationContext();

  // API hooks
  const { data: workflow, isLoading, error } = useApprovalWorkflow(id, organizationId!);
  const updateWorkflowMutation = useUpdateApprovalWorkflow();

  // Handle API errors
  useEffect(() => {
    if (error) {
      toast.current?.showError('Failed to load workflow');
    }
  }, [error]);

  // Handle form submission
  const handleSubmit = async (workflowData: UpdateApprovalWorkflowRequest) => {
    if (!id) return;

    try {
      await updateWorkflowMutation.mutateAsync({
        id,
        request: workflowData,
        organizationId: organizationId as number,
      });

      // Show success toast and navigate back
      toast.current?.showSuccess('Approval workflow updated successfully');

      // Navigate back to workflows list after a short delay
      setTimeout(() => {
        navigate({ to: approvalWorkflowsRoute.to });
      }, 1500);
    } catch (error) {
      console.error('Error updating workflow:', error);
      toast.current?.showError('Failed to update approval workflow');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: approvalWorkflowsRoute.to });
  };

  // Show error state if workflow not found
  if (error) {
    return (
      <div className="edit-approval-workflow p-4">
        <Toast ref={toast} position="top-right" />
        <Typography variant='h5' weight='semibold' className="mb-4">Edit Approval Workflow</Typography>
          <div className="py-4">
            <i className="pi pi-exclamation-triangle text-orange-500 text-4xl mb-3"></i>
            <p className="text-600 mb-4">
              The approval workflow you're looking for doesn't exist or may have been deleted.
            </p>
            <button
              className="p-button p-button-primary"
              onClick={handleCancel}
            >
              Back to Workflows
            </button>
          </div>
      </div>
    );
  }

  // Show loading state while fetching workflow
  if (isLoading || !workflow) {
    return (
      <div className="edit-approval-workflow p-4">
        <Toast ref={toast} position="top-right" />
        <Typography variant='h5' weight='semibold' className="mb-4">Edit Approval Workflow</Typography>
          <div className="flex justify-content-center py-4">
            <i className="pi pi-spin pi-spinner text-2xl text-primary"></i>
          </div>
      </div>
    );
  }

  return (
    <div className="edit-approval-workflow p-4">
      <Toast ref={toast} position="top-right" />
        <Typography variant='h5' weight='semibold' className="mb-4">Edit Approval Workflow</Typography>
        <ApprovalWorkflowForm
          workflow={workflow}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={updateWorkflowMutation.isPending}
        />
    </div>
  );
};

export default EditApprovalWorkflow;
