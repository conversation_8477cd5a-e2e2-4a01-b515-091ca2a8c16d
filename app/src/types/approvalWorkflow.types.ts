/**
 * Approval Workflow types and interfaces
 */

/**
 * Criteria field types for workflow conditions
 */
export type CriteriaFieldType = 'department' | 'designation' | 'location' | 'employee';

/**
 * Operators for criteria conditions
 */
export type CriteriaOperator = 'IS' | 'IS_NOT';

/**
 * Logic connectors between criteria
 */
export type LogicConnector = 'AND' | 'OR';

/**
 * Workflow action types for approval workflows
 */
export type WorkflowActionType = 'workflow-levels' | 'department-head' | 'department-head-record-owner' | 'specific-user';

/**
 * Approver types for the approvals JSON structure
 */
export type ApproverType = 'REPORTING_TO' | 'DEPARTMENT_HEAD' | 'DEPARTMENT_HEAD_RECORD_OWNER' | 'USER';

/**
 * Individual approval action in the approvals array
 */
export interface ApprovalAction {
  approver: number; // User ID, department head ID, or level number
  approverType: ApproverType;
}

/**
 * Individual criteria condition
 */
export interface WorkflowCriteria {
  id: string;
  fieldType: CriteriaFieldType;
  operator: CriteriaOperator;
  values: number[]; // Array of IDs for the selected values (e.g., department IDs, etc.)
  logicConnector?: LogicConnector; // Not needed for the last criteria
}

/**
 * Approval level configuration
 */
export interface ApprovalLevel {
  level: number;
  name: string;
  description: string;
}

/**
 * Complete approval workflow configuration
 */
export interface ApprovalWorkflow {
  id?: string;
  name: string;
  formId: number;
  formType: string;
  criteria: WorkflowCriteria[];
  approvalLevels: number; // Number of approval levels (1, 2, 3, etc.)
  autoApprove: boolean;
  autoReject: boolean;
  isActive: boolean;
  createdDate?: string;
  lastModifiedDate?: string;
  // New fields for enhanced workflow actions
  workflowActionType?: WorkflowActionType;
  selectedDepartmentId?: string; // For department-head action type
  selectedUserId?: number; // For specific-user action type
  approvals?: ApprovalAction[]; // New JSON structure for backend
}

/**
 * Mock data interfaces for dropdown options
 */
export interface DropdownOption {
  label: string;
  value: string;
}

/**
 * Department option for criteria
 */
export interface DepartmentOption extends DropdownOption {
  id: string;
  description?: string;
}

/**
 * Designation option for criteria
 */
export interface DesignationOption extends DropdownOption {
  id: string;
  description?: string;
}

/**
 * Location option for criteria
 */
export interface LocationOption extends DropdownOption {
  id: string;
  city: string;
  state: string;
}

/**
 * Employee option for criteria
 */
export interface EmployeeOption extends DropdownOption {
  id: string;
  email: string;
  department: string;
  designation: string;
}

/**
 * Form type options
 */
export interface FormTypeOption {
  label: string;
  value: number; // Form ID as number to match formId field
  description: string;
}

/**
 * Request object for creating a new approval workflow
 */
export interface CreateApprovalWorkflowRequest {
  name: string;
  formId: number;
  criteria: WorkflowCriteria[];
  approvalLevels: number;
  autoApprove: boolean;
  autoReject: boolean;
  // New fields for enhanced workflow actions
  workflowActionType?: WorkflowActionType;
  selectedDepartmentId?: string;
  selectedUserId?: number;
  approvals?: ApprovalAction[];
}

/**
 * Request object for updating an existing approval workflow
 */
export interface UpdateApprovalWorkflowRequest {
  name?: string;
  formId?: number;
  criteria?: WorkflowCriteria[];
  approvalLevels?: number;
  autoApprove?: boolean;
  autoReject?: boolean;
  isActive?: boolean;
  // New fields for enhanced workflow actions
  workflowActionType?: WorkflowActionType;
  selectedDepartmentId?: string;
  selectedUserId?: number;
  approvals?: ApprovalAction[];
}

/**
 * Workflow preview data for visual display
 */
export interface WorkflowPreview {
  triggerConditions: string[];
  approvalPath: string[];
  finalAction: 'approve' | 'reject' | 'manual';
}

/**
 * Record approval response from backend
 */
export interface RecordApprovalResponse {
  id: number;
  organizationId: number;
  formId: number;
  recordId: number;
  initiatorUserId: number;
  initiatorName: string;
  status: ApprovalStatus;
  formName: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  currentApproverName: string;
}

/**
 * Approval status enum
 */
export enum ApprovalStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

/**
 * Approval action request for approve/reject operations
 */
export interface ApprovalActionRequest {
  userId: number;
  comment?: string;
}